import React from 'react';

/**
 * AnimatedGradientBackground component creates a dynamic gradient background
 * with smooth color transitions and gentle movement
 */
const AnimatedGradientBackground: React.FC = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {/* Main animated gradient background */}
      <div 
        className="absolute inset-0 opacity-50 animate-gradient-shift"
        style={{
          background: 'linear-gradient(-45deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.13), rgba(59, 130, 246, 0.15), rgba(99, 102, 241, 0.11))',
          backgroundSize: '400% 400%',
        }}
      />
      
      {/* Secondary gradient layer for depth */}
      <div 
        className="absolute inset-0 opacity-35 animate-gradient-shift-reverse"
        style={{
          background: 'radial-gradient(ellipse at 20% 50%, rgba(244, 114, 182, 0.12) 0%, transparent 50%), radial-gradient(ellipse at 80% 20%, rgba(34, 211, 238, 0.10) 0%, transparent 50%), radial-gradient(ellipse at 40% 80%, rgba(139, 92, 246, 0.11) 0%, transparent 50%)',
          backgroundSize: '300% 300%',
        }}
      />

      {/* Floating orbs for visual interest */}
      <div 
        className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full opacity-25 animate-float-slow"
        style={{
          background: 'radial-gradient(circle, rgba(99, 102, 241, 0.20) 0%, transparent 70%)',
          filter: 'blur(40px)',
        }}
      />
      
      <div 
        className="absolute top-3/4 right-1/3 w-48 h-48 rounded-full opacity-20 animate-float-medium"
        style={{
          background: 'radial-gradient(circle, rgba(244, 114, 182, 0.18) 0%, transparent 70%)',
          filter: 'blur(30px)',
        }}
      />
      
      <div 
        className="absolute top-1/2 right-1/4 w-32 h-32 rounded-full opacity-30 animate-float-fast"
        style={{
          background: 'radial-gradient(circle, rgba(34, 211, 238, 0.22) 0%, transparent 70%)',
          filter: 'blur(25px)',
        }}
      />
    </div>
  );
};

export default AnimatedGradientBackground;
