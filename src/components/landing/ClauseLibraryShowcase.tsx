import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Plus,
  Star,
  Heart,
  Tag,
  Globe,
  BookOpen,
  GripVertical,
  ArrowRight,
  Sparkles,
  Shield,
  Zap
} from 'lucide-react';

const ClauseLibraryShowcase = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isBuilding, setIsBuilding] = useState(false);
  const [selectedClauses, setSelectedClauses] = useState<string[]>([]);

  // Mock clause library data based on the actual app
  const clauseLibrary = [
    {
      id: 'termination-1',
      title: 'Standard Termination Clause',
      content: 'Either party may terminate this Agreement at any time by providing thirty (30) days written notice to the other party.',
      category: 'Termination',
      jurisdiction: ['US', 'UK', 'CA'],
      riskLevel: 'low',
      popularity: 95,
      rating: 4.8,
      tags: ['termination', 'notice', 'standard'],
      isFavorite: false,
      description: 'Basic termination clause allowing either party to end the agreement with notice'
    },
    {
      id: 'confidentiality-1',
      title: 'Mutual Confidentiality Agreement',
      content: 'Each party acknowledges that it may have access to certain confidential information of the other party. Each party agrees to maintain in confidence all confidential information received from the other party.',
      category: 'Confidentiality',
      jurisdiction: ['US', 'UK', 'EU'],
      riskLevel: 'medium',
      popularity: 88,
      rating: 4.6,
      tags: ['confidentiality', 'mutual', 'information'],
      isFavorite: true,
      description: 'Protects confidential information shared between parties'
    },
    {
      id: 'payment-net30',
      title: 'Net 30 Payment Terms',
      content: 'Payment is due within thirty (30) days of invoice date. Late payments may incur a service charge of 1.5% per month.',
      category: 'Payment',
      jurisdiction: ['US', 'CA'],
      riskLevel: 'low',
      popularity: 92,
      rating: 4.7,
      tags: ['payment', 'net 30', 'invoice'],
      isFavorite: true,
      description: 'Standard 30-day payment terms with late fee provision'
    },
    {
      id: 'ip-tech-1',
      title: 'Technology IP Ownership',
      content: 'All intellectual property rights in any technology, software, or innovations developed during the performance of this Agreement shall remain the exclusive property of the Company.',
      category: 'Intellectual Property',
      jurisdiction: ['US', 'UK'],
      riskLevel: 'high',
      popularity: 76,
      rating: 4.4,
      tags: ['intellectual property', 'technology', 'ownership'],
      isFavorite: false,
      description: 'Establishes clear IP ownership for technology-related work'
    },
    {
      id: 'force-majeure-1',
      title: 'Force Majeure Clause',
      content: 'Neither party shall be liable for any failure or delay in performance under this Agreement which is due to an act of God, war, terrorism, epidemic, government action, or other causes beyond the reasonable control of such party.',
      category: 'Risk Management',
      jurisdiction: ['US', 'UK', 'EU'],
      riskLevel: 'medium',
      popularity: 84,
      rating: 4.5,
      tags: ['force majeure', 'risk', 'liability'],
      isFavorite: false,
      description: 'Protects parties from liability due to extraordinary circumstances'
    }
  ];

  const categories = ['All Categories', 'Termination', 'Confidentiality', 'Payment', 'Intellectual Property', 'Risk Management'];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const toggleClauseSelection = (clauseId: string) => {
    setSelectedClauses(prev => 
      prev.includes(clauseId) 
        ? prev.filter(id => id !== clauseId)
        : [...prev, clauseId]
    );
  };

  const startBuilding = () => {
    setIsBuilding(true);
    setTimeout(() => setIsBuilding(false), 2000);
  };

  return (
    <section className="py-16 sm:py-24 bg-gradient-to-br from-muted/30 via-background to-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-4">
            <BookOpen className="w-4 h-4" />
            Clause Library Builder
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Build Contracts with 
            <span className="block text-primary">Pre-Approved Clauses</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
            Drag and drop from our extensive library of legal clauses. Each clause is pre-reviewed, 
            risk-assessed, and optimized for different jurisdictions and industries.
          </p>
        </div>

        {/* Main Demo Interface */}
        <div className="bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto">
          {/* Apple-style window header */}
          <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex-1 flex items-center justify-center">
              <span className="text-sm text-muted-foreground">LegalAI - Clause Library Builder</span>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-0">
            {/* Left Side - Clause Library */}
            <div className="p-6 border-r">
              <div className="space-y-4">
                {/* Library Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold">Clause Library</h3>
                    <Badge variant="outline">{clauseLibrary.length} clauses</Badge>
                  </div>
                  <Button size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Custom
                  </Button>
                </div>

                {/* Search and Filter */}
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search clauses..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    {categories.slice(0, 4).map((category) => (
                      <Button
                        key={category}
                        variant={selectedCategory === category ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedCategory(category)}
                        className="text-xs"
                      >
                        {category}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Clause List */}
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {clauseLibrary.slice(0, 4).map((clause) => (
                    <div
                      key={clause.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                        selectedClauses.includes(clause.id) ? 'border-primary bg-primary/5' : 'hover:border-primary/30'
                      }`}
                      onClick={() => toggleClauseSelection(clause.id)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{clause.title}</h4>
                            {clause.isFavorite && <Heart className="h-3 w-3 fill-red-500 text-red-500" />}
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">
                            {clause.description}
                          </p>
                        </div>
                        <Badge className={`${getRiskColor(clause.riskLevel)} text-xs`}>
                          {clause.riskLevel}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            {clause.rating}
                          </div>
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            {clause.category}
                          </div>
                        </div>
                        <div className="flex gap-1">
                          {clause.jurisdiction.slice(0, 2).map(j => (
                            <Badge key={j} variant="outline" className="text-xs">
                              {j}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Side - Contract Builder */}
            <div className="p-6">
              <div className="space-y-4">
                {/* Builder Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold">Contract Builder</h3>
                    {selectedClauses.length > 0 && (
                      <Badge variant="outline">{selectedClauses.length} selected</Badge>
                    )}
                  </div>
                  <Button 
                    size="sm" 
                    onClick={startBuilding}
                    disabled={selectedClauses.length === 0}
                  >
                    {isBuilding ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Building...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Build Contract
                      </>
                    )}
                  </Button>
                </div>

                {selectedClauses.length === 0 ? (
                  <div className="border-2 border-dashed border-muted-foreground/20 rounded-lg p-8 text-center">
                    <div className="flex justify-center mb-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <ArrowRight className="w-6 h-6 text-primary rotate-180" />
                      </div>
                    </div>
                    <p className="text-sm font-medium mb-2">Start Building Your Contract</p>
                    <p className="text-xs text-muted-foreground">
                      Select clauses from the library to build your contract
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="text-xs text-muted-foreground mb-2">
                      Selected Clauses ({selectedClauses.length})
                    </div>
                    {selectedClauses.map((clauseId, index) => {
                      const clause = clauseLibrary.find(c => c.id === clauseId);
                      if (!clause) return null;
                      
                      return (
                        <div key={clauseId} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                            <span className="text-xs font-mono bg-primary/10 text-primary px-2 py-1 rounded">
                              {index + 1}
                            </span>
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{clause.title}</div>
                            <div className="text-xs text-muted-foreground">{clause.category}</div>
                          </div>
                          <Badge className={`${getRiskColor(clause.riskLevel)} text-xs`}>
                            {clause.riskLevel}
                          </Badge>
                        </div>
                      );
                    })}

                    {/* AI Insights */}
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">AI Contract Analysis</span>
                      </div>
                      <div className="space-y-2 text-xs text-blue-800">
                        <div className="flex items-center justify-between">
                          <span>Compliance Score</span>
                          <span className="font-medium">94%</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Risk Level</span>
                          <Badge className="bg-green-100 text-green-800 text-xs">Low</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Missing Clauses</span>
                          <span className="text-yellow-600">2 suggested</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Bottom Action Bar */}
          <div className="border-t bg-muted/30 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <span>AI-Powered Suggestions</span>
                </div>
                <div className="flex items-center gap-1">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span>Risk Assessment</span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4 text-blue-500" />
                  <span>Multi-Jurisdiction</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  Save Template
                </Button>
                <Button size="sm">
                  Generate Contract
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="grid md:grid-cols-3 gap-6 mt-12">
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Extensive Library</h3>
            <p className="text-sm text-muted-foreground">
              500+ pre-approved clauses across all major practice areas and jurisdictions
            </p>
          </div>
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">AI-Powered Assembly</h3>
            <p className="text-sm text-muted-foreground">
              Smart suggestions and real-time compliance checking as you build
            </p>
          </div>
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Risk Assessment</h3>
            <p className="text-sm text-muted-foreground">
              Every clause is risk-rated and comes with legal guidance and alternatives
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClauseLibraryShowcase;
